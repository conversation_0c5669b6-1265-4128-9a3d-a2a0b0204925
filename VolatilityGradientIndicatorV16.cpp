#include "sierrachart.h"
#include <cmath>
#include <algorithm>
#include <vector>

SCDLLName("VolatilityGradientIndicator")

/*==========================================================================*/
// VOLATILITY GRADIENT CANDLE INDICATOR - 10 VOLATILITY METHODS, 20-COLOR GRADIENT
//
// VOLATILITY SCALING EXPLANATION:
// - ATR: Price units (e.g., 1.18 = $1.18 average true range)
// - Log-return methods: Annualized percentage (e.g., 15.87 = 15.87% annual volatility)
//   (StdDev, EWMA, Yang-Zhang, Garman-<PERSON>lass, Rogers-<PERSON>, Parkinson, Realized Vol)
// - Range Ratio: Ratio (e.g., 0.024 = 2.4% of price)
// - Z-Score: Standardized units (e.g., 2.5 = 2.5 standard deviations)
// Deep Blue (Low Vol) → Green (Medium Vol) → Bright Red (High Vol)
/*==========================================================================*/

// Helper function to calculate log returns
float CalculateLogReturn(float currentClose, float previousClose)
{
    if (previousClose <= 0.0f) return 0.0f;
    return logf(currentClose / previousClose);
}

// Helper function to calculate True Range
float CalculateTrueRange(float high, float low, float prevClose)
{
    float range1 = high - low;
    float range2 = fabsf(high - prevClose);
    float range3 = fabsf(low - prevClose);

    // Use manual comparison to avoid std::max macro conflicts
    float maxRange = range1;
    if (range2 > maxRange) maxRange = range2;
    if (range3 > maxRange) maxRange = range3;

    return maxRange;
}

// Helper function to normalize value using rolling min-max with PROPER soft clipping
float NormalizeValue(SCStudyInterfaceRef sc, SCFloatArrayRef values, int currentIndex,
                    int normalizationWindow, float softClipPercent, int arraySize)
{
    if (currentIndex < 1 || arraySize < 2) return 0.0f;

    int startIdx = (currentIndex - normalizationWindow + 1 > 0) ?
                   (currentIndex - normalizationWindow + 1) : 0;
    int endIdx = currentIndex;

    // Collect values for normalization
    std::vector<float> windowValues;

    for (int i = startIdx; i <= endIdx; i++)
    {
        if (i < arraySize && values[i] > 0.0f) // Only exclude truly invalid values, allow small positive values
        {
            windowValues.push_back(values[i]);
        }
    }

    if (windowValues.empty()) return 0.0f;

    // Apply PROPER soft clipping if enabled - remove extreme values from BOTH ends
    if (softClipPercent > 0.0f && windowValues.size() >= 3) // Reduced minimum requirement
    {
        // Sort volatility values by magnitude
        std::sort(windowValues.begin(), windowValues.end());

        // Calculate how many values to remove from EACH end
        // softClipPercent is total percentage, so divide by 2 for each end, then by 100 for percentage
        int removeFromEachEnd = static_cast<int>(windowValues.size() * softClipPercent / 200.0f);

        // Remove extreme values from both ends
        if (removeFromEachEnd > 0 && removeFromEachEnd < static_cast<int>(windowValues.size()) / 2)
        {
            std::vector<float> clippedValues;
            for (size_t i = removeFromEachEnd; i < windowValues.size() - removeFromEachEnd; i++)
            {
                clippedValues.push_back(windowValues[i]);
            }
            windowValues = clippedValues;
        }
    }

    if (windowValues.empty()) return 0.0f;

    // Find min and max from clipped values - PROPERLY handle sorted vs unsorted data
    float minVal = *std::min_element(windowValues.begin(), windowValues.end());
    float maxVal = *std::max_element(windowValues.begin(), windowValues.end());

    float currentValue = values[currentIndex];

    // Handle edge case where current value is invalid
    if (currentValue <= 0.0f) return 0.0f;

    // CRITICAL FIX: Handle very small ranges properly instead of returning 0.0f
    float range = maxVal - minVal;
    if (range < 0.0001f) // Very small range threshold
    {
        // For very small ranges, use relative position based on current value vs average
        float avgVal = (minVal + maxVal) / 2.0f;
        if (currentValue < avgVal) return 0.3f; // Below average
        if (currentValue > avgVal) return 0.7f; // Above average
        return 0.5f; // At average
    }

    // Enhanced clamping with gradual scaling for outliers
    if (currentValue < minVal)
    {
        // Scale outliers below range to 0.0-0.1 based on how far below
        float ratio = currentValue / minVal;
        return (ratio > 0.5f) ? (ratio * 0.2f) : 0.0f;
    }
    if (currentValue > maxVal)
    {
        // Scale outliers above range to 0.9-1.0 based on how far above
        float ratio = maxVal / currentValue;
        return (ratio > 0.5f) ? (0.8f + ratio * 0.2f) : 1.0f;
    }

    // Normal case: linear interpolation within range
    return (currentValue - minVal) / range;
}

// Helper function to apply EMA smoothing to volatility values
void ApplySmoothing(SCFloatArrayRef rawValues, SCFloatArrayRef smoothedValues, int currentIndex, int smoothingPeriod)
{
    if (currentIndex < 1 || rawValues[currentIndex] == 0.0f) return;

    float alpha = 2.0f / (smoothingPeriod + 1.0f);

    if (currentIndex == 1 || smoothedValues[currentIndex-1] == 0.0f)
    {
        smoothedValues[currentIndex] = rawValues[currentIndex];
    }
    else
    {
        smoothedValues[currentIndex] = alpha * rawValues[currentIndex] + (1.0f - alpha) * smoothedValues[currentIndex-1];
    }
}

// Helper function to get 20-step volatility color gradient (deep blue → green → bright red)
COLORREF GetVolatilityColor(float normalizedValue)
{
    // Clamp value between 0 and 1
    if (normalizedValue < 0.0f) normalizedValue = 0.0f;
    if (normalizedValue > 1.0f) normalizedValue = 1.0f;

    // Define 20-step color gradient: Deep Blue → Green (middle) → Bright Red
    COLORREF colors[20] = {
        RGB(0, 0, 139),       // 0.00-0.05: Deep Blue (lowest volatility)
        RGB(0, 0, 180),       // 0.05-0.10: Dark Blue
        RGB(0, 30, 200),      // 0.10-0.15: Blue
        RGB(0, 60, 220),      // 0.15-0.20: Medium Blue
        RGB(0, 90, 240),      // 0.20-0.25: Light Blue
        RGB(0, 120, 255),     // 0.25-0.30: Bright Blue
        RGB(0, 150, 255),     // 0.30-0.35: Sky Blue
        RGB(0, 180, 240),     // 0.35-0.40: Light Sky Blue
        RGB(0, 200, 200),     // 0.40-0.45: Blue-Cyan
        RGB(0, 220, 160),     // 0.45-0.50: Cyan-Green (approaching middle)
        RGB(0, 255, 100),     // 0.50-0.55: Green (middle - moderate volatility)
        RGB(50, 255, 50),     // 0.55-0.60: Bright Green
        RGB(100, 255, 0),     // 0.60-0.65: Yellow-Green
        RGB(150, 255, 0),     // 0.65-0.70: Lime Green
        RGB(200, 255, 0),     // 0.70-0.75: Yellow
        RGB(255, 200, 0),     // 0.75-0.80: Orange
        RGB(255, 150, 0),     // 0.80-0.85: Dark Orange
        RGB(255, 100, 0),     // 0.85-0.90: Red-Orange
        RGB(255, 50, 0),      // 0.90-0.95: Orange-Red
        RGB(255, 0, 0)        // 0.95-1.00: Bright Red (highest volatility)
    };

    // Determine which color segment we're in (0-19)
    int segment = static_cast<int>(normalizedValue * 19.999f);
    if (segment > 19) segment = 19;
    if (segment < 0) segment = 0;

    // Calculate position within the segment (0.0 to 1.0)
    float segmentPosition = (normalizedValue * 20.0f) - segment;
    if (segmentPosition > 1.0f) segmentPosition = 1.0f;

    // Interpolate between current and next color
    COLORREF color1 = colors[segment];
    COLORREF color2 = (segment < 19) ? colors[segment + 1] : colors[19];

    int r1 = GetRValue(color1), g1 = GetGValue(color1), b1 = GetBValue(color1);
    int r2 = GetRValue(color2), g2 = GetGValue(color2), b2 = GetBValue(color2);

    int r = static_cast<int>(r1 + (r2 - r1) * segmentPosition);
    int g = static_cast<int>(g1 + (g2 - g1) * segmentPosition);
    int b = static_cast<int>(b1 + (b2 - b1) * segmentPosition);

    return RGB(r, g, b);
}

/*==========================================================================*/
SCSFExport scsf_VolatilityGradient(SCStudyInterfaceRef sc)
{
    // Basic Settings
    SCInputRef NormalizationWindow = sc.Input[0];
    SCInputRef SoftClipPercent = sc.Input[1];
    SCInputRef EnableSmoothing = sc.Input[2];
    SCInputRef SmoothingPeriod = sc.Input[3];

    // Multi-Vol Feature
    SCInputRef EnableMultiVol = sc.Input[4];

    // Statistics Display Feature
    SCInputRef ShowStatistics = sc.Input[5];
    SCInputRef StatsDistanceAbovePrice = sc.Input[6];

    // Volatility Method Selection (single or multiple based on MultiVol setting)
    SCInputRef UseATR = sc.Input[7];
    SCInputRef UseStdDev = sc.Input[8];
    SCInputRef UseEWMA = sc.Input[9];
    SCInputRef UseYangZhang = sc.Input[10];
    SCInputRef UseGarmanKlass = sc.Input[11];
    SCInputRef UseRogersSatchell = sc.Input[12];
    SCInputRef UseParkinson = sc.Input[13];
    SCInputRef UseRangeRatio = sc.Input[14];
    SCInputRef UseZScore = sc.Input[15];
    SCInputRef UseRealizedVol = sc.Input[16];

    // Method-specific parameters
    SCInputRef ATRPeriod = sc.Input[17];
    SCInputRef StdDevPeriod = sc.Input[18];
    SCInputRef EWMALambda = sc.Input[19];
    SCInputRef YZPeriod = sc.Input[20];
    SCInputRef GKPeriod = sc.Input[21];
    SCInputRef RSPeriod = sc.Input[22];
    SCInputRef ParkinsonPeriod = sc.Input[23];
    SCInputRef RangeRatioPeriod = sc.Input[24];
    SCInputRef ZScorePeriod = sc.Input[25];
    SCInputRef RealizedVolPeriod = sc.Input[26];
    
    // Note: Using fixed 20-color gradient, no user color inputs needed
    
    // Working arrays
    SCFloatArrayRef VolatilityValues = sc.Subgraph[0].Arrays[0];
    SCFloatArrayRef SmoothedValues = sc.Subgraph[0].Arrays[1];
    SCFloatArrayRef NormalizedValues = sc.Subgraph[0].Arrays[2];
    SCFloatArrayRef LogReturns = sc.Subgraph[0].Arrays[3];
    SCFloatArrayRef TrueRanges = sc.Subgraph[0].Arrays[4];

    // Individual volatility method arrays for multi-vol feature
    SCFloatArrayRef ATRValues = sc.Subgraph[0].Arrays[5];
    SCFloatArrayRef StdDevValues = sc.Subgraph[0].Arrays[6];
    SCFloatArrayRef EWMAValues = sc.Subgraph[0].Arrays[7];
    SCFloatArrayRef YZValues = sc.Subgraph[0].Arrays[8];
    SCFloatArrayRef GKValues = sc.Subgraph[0].Arrays[9];
    SCFloatArrayRef RSValues = sc.Subgraph[0].Arrays[10];
    SCFloatArrayRef ParkinsonValues = sc.Subgraph[0].Arrays[11];
    SCFloatArrayRef RangeRatioValues = sc.Subgraph[0].Arrays[12];
    SCFloatArrayRef ZScoreValues = sc.Subgraph[0].Arrays[13];
    SCFloatArrayRef RealizedVolValues = sc.Subgraph[0].Arrays[14];

    // Smoothed arrays for each method (when smoothing is enabled)
    SCFloatArrayRef ATRSmoothed = sc.Subgraph[1].Arrays[0];
    SCFloatArrayRef StdDevSmoothed = sc.Subgraph[1].Arrays[1];
    SCFloatArrayRef EWMASmoothed = sc.Subgraph[1].Arrays[2];
    SCFloatArrayRef YZSmoothed = sc.Subgraph[1].Arrays[3];
    SCFloatArrayRef GKSmoothed = sc.Subgraph[1].Arrays[4];
    SCFloatArrayRef RSSmoothed = sc.Subgraph[1].Arrays[5];
    SCFloatArrayRef ParkinsonSmoothed = sc.Subgraph[1].Arrays[6];
    SCFloatArrayRef RangeRatioSmoothed = sc.Subgraph[1].Arrays[7];
    SCFloatArrayRef ZScoreSmoothed = sc.Subgraph[1].Arrays[8];
    SCFloatArrayRef RealizedVolSmoothed = sc.Subgraph[1].Arrays[9];
    
    if (sc.SetDefaults)
    {
        sc.GraphName = "Volatility Gradient Candles";
        sc.StudyDescription = "Colors main price bars using 10 volatility methods with 20-step gradient. Single mode: one method. Multi-Vol mode: ensemble average. Statistics display updates only on new bars or setting changes.";
        sc.AutoLoop = 1;
        sc.UpdateAlways = 1;
        sc.GraphRegion = 0;
        sc.FreeDLL = 0; // Keep DLL loaded for better performance
        sc.ValueFormat = 4;
        sc.ScaleRangeType = SCALE_SAMEASREGION;

        // Set up subgraph for coloring bars (both outline and fill)
        sc.Subgraph[0].Name = "Volatility Gradient";
        sc.Subgraph[0].DrawStyle = DRAWSTYLE_COLOR_BAR_CANDLE_FILL;
        sc.Subgraph[0].PrimaryColor = RGB(0, 100, 255); // Default blue for low volatility
        sc.Subgraph[0].DrawZeros = 1; // Ensure all bars are colored
        sc.Subgraph[0].LineWidth = 1;
        
        // Basic Settings
        NormalizationWindow.Name = "Normalization Window";
        NormalizationWindow.SetDescription("Rolling window size for min-max normalization (bars)");
        NormalizationWindow.SetInt(125); // OPTIMIZED: 2x faster response (was 250)
        
        SoftClipPercent.Name = "Soft Clip Percentage";
        SoftClipPercent.SetDescription("Percentage of extreme volatility values to exclude from BOTH ends (0-20%). E.g., 10% removes 5% from each end.");
        SoftClipPercent.SetFloat(5.0f); // Default 5%
        
        EnableSmoothing.Name = "Enable Smoothing";
        EnableSmoothing.SetDescription("Apply EMA smoothing to volatility values");
        EnableSmoothing.SetYesNo(0); // Default disabled

        SmoothingPeriod.Name = "Smoothing Period";
        SmoothingPeriod.SetDescription("EMA period for smoothing volatility values");
        SmoothingPeriod.SetInt(2); // OPTIMIZED: Faster response (was 3)

        // Multi-Vol Feature
        EnableMultiVol.Name = "Enable Multi-Vol Ensemble";
        EnableMultiVol.SetDescription("Allow multiple volatility methods to be combined (averaged). When OFF, only one method can be active.");
        EnableMultiVol.SetYesNo(0); // Default disabled (single method mode)

        // Statistics Display Feature
        ShowStatistics.Name = "Show Statistics Display";
        ShowStatistics.SetDescription("Display raw volatility values and normalized gradient values for active methods in top-right corner");
        ShowStatistics.SetYesNo(1); // Default enabled

        StatsDistanceAbovePrice.Name = "Statistics Distance Above Price (Points)";
        StatsDistanceAbovePrice.SetDescription("Number of points above current price to display statistics (default: 20)");
        StatsDistanceAbovePrice.SetInt(20); // Default 20 points above price

        // Volatility Methods (single or multiple based on MultiVol setting)
        UseATR.Name = "Use ATR (Average True Range)";
        UseATR.SetDescription("Use Average True Range volatility calculation");
        UseATR.SetYesNo(1); // Default method
        
        UseStdDev.Name = "Use Standard Deviation";
        UseStdDev.SetDescription("Use historical volatility (standard deviation of returns)");
        UseStdDev.SetYesNo(0);
        
        UseEWMA.Name = "Use EWMA Volatility";
        UseEWMA.SetDescription("Use Exponentially Weighted Moving Average volatility");
        UseEWMA.SetYesNo(0);
        
        UseYangZhang.Name = "Use Yang-Zhang Volatility";
        UseYangZhang.SetDescription("Use Yang-Zhang volatility estimator");
        UseYangZhang.SetYesNo(0);
        
        UseGarmanKlass.Name = "Use Garman-Klass Volatility";
        UseGarmanKlass.SetDescription("Use Garman-Klass volatility estimator");
        UseGarmanKlass.SetYesNo(0);
        
        UseRogersSatchell.Name = "Use Rogers-Satchell Volatility";
        UseRogersSatchell.SetDescription("Use Rogers-Satchell volatility estimator");
        UseRogersSatchell.SetYesNo(0);
        
        UseParkinson.Name = "Use Parkinson Volatility";
        UseParkinson.SetDescription("Use Parkinson volatility estimator");
        UseParkinson.SetYesNo(0);
        
        UseRangeRatio.Name = "Use Range Ratio";
        UseRangeRatio.SetDescription("Use Range Ratio volatility measure");
        UseRangeRatio.SetYesNo(0);
        
        UseZScore.Name = "Use Z-Score of Returns";
        UseZScore.SetDescription("Use Z-Score of returns as volatility measure");
        UseZScore.SetYesNo(0);
        
        UseRealizedVol.Name = "Use Realized Volatility";
        UseRealizedVol.SetDescription("Use Realized Volatility calculation");
        UseRealizedVol.SetYesNo(0);

        // Method-specific parameters (optimized based on research)
        ATRPeriod.Name = "ATR Period";
        ATRPeriod.SetDescription("Period for ATR calculation");
        ATRPeriod.SetInt(7); // OPTIMIZED: 2x faster response (was 14)

        StdDevPeriod.Name = "Standard Deviation Period";
        StdDevPeriod.SetDescription("Period for standard deviation calculation");
        StdDevPeriod.SetInt(10); // OPTIMIZED: 2x faster response (was 20)

        EWMALambda.Name = "EWMA Lambda";
        EWMALambda.SetDescription("Lambda decay factor for EWMA (0.80-0.99)");
        EWMALambda.SetFloat(0.88f); // OPTIMIZED: Much faster response (was 0.97)

        YZPeriod.Name = "Yang-Zhang Period";
        YZPeriod.SetDescription("Period for Yang-Zhang volatility calculation");
        YZPeriod.SetInt(10); // OPTIMIZED: 2x faster response (was 20)

        GKPeriod.Name = "Garman-Klass Period";
        GKPeriod.SetDescription("Period for Garman-Klass volatility calculation");
        GKPeriod.SetInt(10); // OPTIMIZED: 2x faster response (was 20)

        RSPeriod.Name = "Rogers-Satchell Period";
        RSPeriod.SetDescription("Period for Rogers-Satchell volatility calculation");
        RSPeriod.SetInt(10); // OPTIMIZED: 2x faster response (was 20)

        ParkinsonPeriod.Name = "Parkinson Period";
        ParkinsonPeriod.SetDescription("Period for Parkinson volatility calculation");
        ParkinsonPeriod.SetInt(10); // OPTIMIZED: 2x faster response (was 20)

        RangeRatioPeriod.Name = "Range Ratio Period";
        RangeRatioPeriod.SetDescription("Period for Range Ratio calculation");
        RangeRatioPeriod.SetInt(7); // OPTIMIZED: 2x faster response (was 14)

        ZScorePeriod.Name = "Z-Score Period";
        ZScorePeriod.SetDescription("Period for Z-Score calculation");
        ZScorePeriod.SetInt(10); // OPTIMIZED: 2x faster response (was 20)

        RealizedVolPeriod.Name = "Realized Volatility Period";
        RealizedVolPeriod.SetDescription("Period for Realized Volatility calculation");
        RealizedVolPeriod.SetInt(10); // OPTIMIZED: 2x faster response (was 20)

        // Note: Using fixed 20-color gradient: Deep Blue → Green (middle) → Bright Red
        // Colors automatically assigned based on normalized volatility value (0.0-1.0)
        // Smoother transitions with 20 color steps for better visual clarity

        return;
    }

    int idx = sc.Index;
    if (idx < 1) return;

    // CRITICAL: Calculate volatility using data UP TO previous bar (idx-1)
    // This ensures the color is assigned to the CURRENT candle at its OPEN
    // based on historical data, not after the candle completes
    int calcIdx = idx - 1; // Use previous completed candle for calculation
    if (calcIdx < 1)
    {
        // For very first bars, use lowest volatility color
        sc.Subgraph[0].DataColor[idx] = GetVolatilityColor(0.0f);
        sc.Subgraph[0][idx] = 0.0f;
        return;
    }

    // Method selection enforcement based on Multi-Vol setting
    int activeMethodCount = 0;
    if (UseATR.GetYesNo()) activeMethodCount++;
    if (UseStdDev.GetYesNo()) activeMethodCount++;
    if (UseEWMA.GetYesNo()) activeMethodCount++;
    if (UseYangZhang.GetYesNo()) activeMethodCount++;
    if (UseGarmanKlass.GetYesNo()) activeMethodCount++;
    if (UseRogersSatchell.GetYesNo()) activeMethodCount++;
    if (UseParkinson.GetYesNo()) activeMethodCount++;
    if (UseRangeRatio.GetYesNo()) activeMethodCount++;
    if (UseZScore.GetYesNo()) activeMethodCount++;
    if (UseRealizedVol.GetYesNo()) activeMethodCount++;

    if (!EnableMultiVol.GetYesNo())
    {
        // SINGLE METHOD MODE: Only one method can be active
        if (activeMethodCount != 1)
        {
            // Default to ATR if no method or multiple methods selected
            if (activeMethodCount == 0)
            {
                UseATR.SetYesNo(1);
            }
            else
            {
                // Turn off all except the first active one found
                bool foundFirst = false;
                if (UseATR.GetYesNo() && !foundFirst) { foundFirst = true; }
                else { UseATR.SetYesNo(0); }
                if (UseStdDev.GetYesNo() && !foundFirst) { foundFirst = true; }
                else { UseStdDev.SetYesNo(0); }
                if (UseEWMA.GetYesNo() && !foundFirst) { foundFirst = true; }
                else { UseEWMA.SetYesNo(0); }
                if (UseYangZhang.GetYesNo() && !foundFirst) { foundFirst = true; }
                else { UseYangZhang.SetYesNo(0); }
                if (UseGarmanKlass.GetYesNo() && !foundFirst) { foundFirst = true; }
                else { UseGarmanKlass.SetYesNo(0); }
                if (UseRogersSatchell.GetYesNo() && !foundFirst) { foundFirst = true; }
                else { UseRogersSatchell.SetYesNo(0); }
                if (UseParkinson.GetYesNo() && !foundFirst) { foundFirst = true; }
                else { UseParkinson.SetYesNo(0); }
                if (UseRangeRatio.GetYesNo() && !foundFirst) { foundFirst = true; }
                else { UseRangeRatio.SetYesNo(0); }
                if (UseZScore.GetYesNo() && !foundFirst) { foundFirst = true; }
                else { UseZScore.SetYesNo(0); }
                if (UseRealizedVol.GetYesNo() && !foundFirst) { foundFirst = true; }
                else { UseRealizedVol.SetYesNo(0); }
            }
            // Settings changed - force recalculation but continue with current bar
            sc.UpdateAlways = 1;
        }
    }
    else
    {
        // MULTI-VOL MODE: At least one method must be active
        if (activeMethodCount == 0)
        {
            UseATR.SetYesNo(1); // Default to ATR if none selected
            // Continue with ATR enabled instead of returning
        }
    }

    // Force full recalculation when ANY setting changes to ensure statistics update immediately
    static int lastNormWindow = -1;
    static float lastSoftClip = -1.0f;
    static int lastMultiVolSetting = -1;
    static int lastShowStatsSetting = -1;
    static int lastStatsDistance = -1;
    static int lastSmoothingSetting = -1;
    static int lastSmoothingPeriod = -1;
    static int lastATRSetting = -1;
    static int lastStdDevSetting = -1;
    static int lastEWMASetting = -1;
    static int lastYZSetting = -1;
    static int lastGKSetting = -1;
    static int lastRSSetting = -1;
    static int lastParkinsonSetting = -1;
    static int lastRangeSetting = -1;
    static int lastZScoreSetting = -1;
    static int lastRealizedSetting = -1;
    static int lastATRPeriod = -1;
    static int lastStdDevPeriod = -1;
    static float lastEWMALambda = -1.0f;
    static int lastYZPeriod = -1;
    static int lastGKPeriod = -1;
    static int lastRSPeriod = -1;
    static int lastParkinsonPeriod = -1;
    static int lastRangePeriod = -1;
    static int lastZScorePeriod = -1;
    static int lastRealizedPeriod = -1;

    bool settingsChanged = false;

    // Check ALL settings for changes
    if (lastNormWindow != NormalizationWindow.GetInt()) { lastNormWindow = NormalizationWindow.GetInt(); settingsChanged = true; }
    if (lastSoftClip != SoftClipPercent.GetFloat()) { lastSoftClip = SoftClipPercent.GetFloat(); settingsChanged = true; }
    if (lastMultiVolSetting != EnableMultiVol.GetYesNo()) { lastMultiVolSetting = EnableMultiVol.GetYesNo(); settingsChanged = true; }
    if (lastShowStatsSetting != ShowStatistics.GetYesNo()) { lastShowStatsSetting = ShowStatistics.GetYesNo(); settingsChanged = true; }
    if (lastStatsDistance != StatsDistanceAbovePrice.GetInt()) { lastStatsDistance = StatsDistanceAbovePrice.GetInt(); settingsChanged = true; }
    if (lastSmoothingSetting != EnableSmoothing.GetYesNo()) { lastSmoothingSetting = EnableSmoothing.GetYesNo(); settingsChanged = true; }
    if (lastSmoothingPeriod != SmoothingPeriod.GetInt()) { lastSmoothingPeriod = SmoothingPeriod.GetInt(); settingsChanged = true; }
    if (lastATRSetting != UseATR.GetYesNo()) { lastATRSetting = UseATR.GetYesNo(); settingsChanged = true; }
    if (lastStdDevSetting != UseStdDev.GetYesNo()) { lastStdDevSetting = UseStdDev.GetYesNo(); settingsChanged = true; }
    if (lastEWMASetting != UseEWMA.GetYesNo()) { lastEWMASetting = UseEWMA.GetYesNo(); settingsChanged = true; }
    if (lastYZSetting != UseYangZhang.GetYesNo()) { lastYZSetting = UseYangZhang.GetYesNo(); settingsChanged = true; }
    if (lastGKSetting != UseGarmanKlass.GetYesNo()) { lastGKSetting = UseGarmanKlass.GetYesNo(); settingsChanged = true; }
    if (lastRSSetting != UseRogersSatchell.GetYesNo()) { lastRSSetting = UseRogersSatchell.GetYesNo(); settingsChanged = true; }
    if (lastParkinsonSetting != UseParkinson.GetYesNo()) { lastParkinsonSetting = UseParkinson.GetYesNo(); settingsChanged = true; }
    if (lastRangeSetting != UseRangeRatio.GetYesNo()) { lastRangeSetting = UseRangeRatio.GetYesNo(); settingsChanged = true; }
    if (lastZScoreSetting != UseZScore.GetYesNo()) { lastZScoreSetting = UseZScore.GetYesNo(); settingsChanged = true; }
    if (lastRealizedSetting != UseRealizedVol.GetYesNo()) { lastRealizedSetting = UseRealizedVol.GetYesNo(); settingsChanged = true; }
    if (lastATRPeriod != ATRPeriod.GetInt()) { lastATRPeriod = ATRPeriod.GetInt(); settingsChanged = true; }
    if (lastStdDevPeriod != StdDevPeriod.GetInt()) { lastStdDevPeriod = StdDevPeriod.GetInt(); settingsChanged = true; }
    if (lastEWMALambda != EWMALambda.GetFloat()) { lastEWMALambda = EWMALambda.GetFloat(); settingsChanged = true; }
    if (lastYZPeriod != YZPeriod.GetInt()) { lastYZPeriod = YZPeriod.GetInt(); settingsChanged = true; }
    if (lastGKPeriod != GKPeriod.GetInt()) { lastGKPeriod = GKPeriod.GetInt(); settingsChanged = true; }
    if (lastRSPeriod != RSPeriod.GetInt()) { lastRSPeriod = RSPeriod.GetInt(); settingsChanged = true; }
    if (lastParkinsonPeriod != ParkinsonPeriod.GetInt()) { lastParkinsonPeriod = ParkinsonPeriod.GetInt(); settingsChanged = true; }
    if (lastRangePeriod != RangeRatioPeriod.GetInt()) { lastRangePeriod = RangeRatioPeriod.GetInt(); settingsChanged = true; }
    if (lastZScorePeriod != ZScorePeriod.GetInt()) { lastZScorePeriod = ZScorePeriod.GetInt(); settingsChanged = true; }
    if (lastRealizedPeriod != RealizedVolPeriod.GetInt()) { lastRealizedPeriod = RealizedVolPeriod.GetInt(); settingsChanged = true; }

    if (settingsChanged)
    {
        // Force recalculation of the entire chart (clearing will be done in statistics section)
        sc.UpdateAlways = 1;
    }

    // Initialize arrays to prevent stale values
    if (calcIdx == 1)
    {
        // Clear all volatility arrays on first calculation
        for (int i = 0; i <= calcIdx; i++)
        {
            ATRValues[i] = 0.0f;
            StdDevValues[i] = 0.0f;
            EWMAValues[i] = 0.0f;
            YZValues[i] = 0.0f;
            GKValues[i] = 0.0f;
            RSValues[i] = 0.0f;
            ParkinsonValues[i] = 0.0f;
            RangeRatioValues[i] = 0.0f;
            ZScoreValues[i] = 0.0f;
            RealizedVolValues[i] = 0.0f;
        }
    }

    // Calculate volatility values for all selected methods using PREVIOUS completed data
    // In single mode: only one method active, in multi-vol mode: multiple methods active

    // Calculate individual volatility methods and store in separate arrays

    // 1. ATR (Average True Range)
    if (UseATR.GetYesNo())
    {
        if (calcIdx >= 1)
        {
            float tr = CalculateTrueRange(sc.High[calcIdx], sc.Low[calcIdx], sc.Close[calcIdx-1]);
            TrueRanges[calcIdx] = tr;

            // Calculate EMA of True Range
            // ATR is kept in price units (e.g., $1.18) - this is correct and meaningful
            if (calcIdx == 1)
            {
                ATRValues[calcIdx] = tr;
            }
            else
            {
                float alpha = 2.0f / (ATRPeriod.GetInt() + 1.0f);
                ATRValues[calcIdx] = alpha * tr + (1.0f - alpha) * ATRValues[calcIdx-1];
            }
        }
    }
    // 2. Standard Deviation of Returns
    if (UseStdDev.GetYesNo())
    {
        if (calcIdx >= 1)
        {
            LogReturns[calcIdx] = CalculateLogReturn(sc.Close[calcIdx], sc.Close[calcIdx-1]);

            if (calcIdx >= StdDevPeriod.GetInt())
            {
                // Calculate mean and standard deviation
                float sum = 0.0f;
                int period = StdDevPeriod.GetInt();

                for (int i = calcIdx - period + 1; i <= calcIdx; i++)
                {
                    sum += LogReturns[i];
                }
                float mean = sum / period;

                float sumSquaredDiff = 0.0f;
                for (int i = calcIdx - period + 1; i <= calcIdx; i++)
                {
                    float diff = LogReturns[i] - mean;
                    sumSquaredDiff += diff * diff;
                }

                // Calculate standard deviation and convert to annualized percentage volatility
                float stdDev = sqrtf(sumSquaredDiff / period);
                // Convert log returns to annualized percentage volatility:
                // * 100 = convert to percentage (0.01 -> 1%)
                // * sqrt(252) = annualize assuming 252 trading days per year
                // Result: meaningful percentage like 15.87% annual volatility
                StdDevValues[calcIdx] = stdDev * 100.0f * sqrtf(252.0f);
            }
        }
    }
    // 3. EWMA Volatility
    if (UseEWMA.GetYesNo())
    {
        if (calcIdx >= 1)
        {
            float logReturn = CalculateLogReturn(sc.Close[calcIdx], sc.Close[calcIdx-1]);
            LogReturns[calcIdx] = logReturn;

            float lambda = EWMALambda.GetFloat();
            float squaredReturn = logReturn * logReturn;

            if (calcIdx == 1)
            {
                // Convert to annualized percentage volatility (same scaling as StdDev)
                EWMAValues[calcIdx] = sqrtf(squaredReturn) * 100.0f * sqrtf(252.0f);
            }
            else
            {
                float prevVolScaled = EWMAValues[calcIdx-1] / (100.0f * sqrtf(252.0f)); // Unscale previous
                float ewmaVariance = lambda * prevVolScaled * prevVolScaled + (1.0f - lambda) * squaredReturn;
                // Convert to annualized percentage volatility
                EWMAValues[calcIdx] = sqrtf(ewmaVariance) * 100.0f * sqrtf(252.0f);
            }
        }
    }

    // 4. Yang-Zhang Volatility
    if (UseYangZhang.GetYesNo())
    {
        // Yang-Zhang Volatility Estimator using ROLLING WINDOW of previous data
        if (calcIdx >= YZPeriod.GetInt())
        {
            float sumYZVariance = 0.0f;
            int period = YZPeriod.GetInt();

            // Calculate Yang-Zhang variance for each bar in the window
            for (int i = calcIdx - period + 1; i <= calcIdx; i++)
            {
                float high = sc.High[i];
                float low = sc.Low[i];
                float open = sc.Open[i];
                float close = sc.Close[i];
                float prevClose = (i > 0) ? sc.Close[i-1] : close;

                // Overnight gap variance
                float gapReturn = (prevClose > 0.0f) ? logf(open / prevClose) : 0.0f;
                float gapVariance = gapReturn * gapReturn;

                // Open-to-close variance
                float ocReturn = (open > 0.0f) ? logf(close / open) : 0.0f;
                float ocVariance = ocReturn * ocReturn;

                // Rogers-Satchell component
                float rsComponent = 0.0f;
                if (high > 0.0f && low > 0.0f && open > 0.0f && close > 0.0f)
                {
                    rsComponent = logf(high / open) * logf(high / close) +
                                 logf(low / open) * logf(low / close);
                }

                // Yang-Zhang formula with k = 0.34 for daily data
                float k = 0.34f;
                float yzVariance = k * ocVariance + (1.0f - k) * rsComponent + gapVariance;
                sumYZVariance += yzVariance;
            }

            // Average the variance over the period, then convert to annualized percentage volatility
            float avgVariance = sumYZVariance / period;
            // Yang-Zhang: Convert to annualized percentage volatility (same scaling as other methods)
            YZValues[calcIdx] = sqrtf(fabsf(avgVariance)) * 100.0f * sqrtf(252.0f);
        }
    }

    // 5. Garman-Klass Volatility
    if (UseGarmanKlass.GetYesNo())
    {
        // Garman-Klass Volatility Estimator using ROLLING WINDOW of previous data
        if (calcIdx >= GKPeriod.GetInt())
        {
            float sumGKVariance = 0.0f;
            int period = GKPeriod.GetInt();

            // Calculate Garman-Klass variance for each bar in the window
            for (int i = calcIdx - period + 1; i <= calcIdx; i++)
            {
                float high = sc.High[i];
                float low = sc.Low[i];
                float open = sc.Open[i];
                float close = sc.Close[i];

                if (high > 0.0f && low > 0.0f && open > 0.0f && close > 0.0f)
                {
                    float hlComponent = 0.5f * powf(logf(high / low), 2.0f);
                    float ocComponent = (2.0f * logf(2.0f) - 1.0f) * powf(logf(close / open), 2.0f);
                    float gkVariance = hlComponent - ocComponent;
                    sumGKVariance += gkVariance;
                }
            }

            // Average the variance over the period, then convert to annualized percentage volatility
            float avgVariance = sumGKVariance / period;
            // Garman-Klass: Convert to annualized percentage volatility
            GKValues[calcIdx] = sqrtf(fabsf(avgVariance)) * 100.0f * sqrtf(252.0f);
        }
    }

    // 6. Rogers-Satchell Volatility
    if (UseRogersSatchell.GetYesNo())
    {
        // Rogers-Satchell Volatility Estimator using ROLLING WINDOW of previous data
        if (calcIdx >= RSPeriod.GetInt())
        {
            float sumRSVariance = 0.0f;
            int period = RSPeriod.GetInt();

            // Calculate Rogers-Satchell variance for each bar in the window
            for (int i = calcIdx - period + 1; i <= calcIdx; i++)
            {
                float high = sc.High[i];
                float low = sc.Low[i];
                float open = sc.Open[i];
                float close = sc.Close[i];

                if (high > 0.0f && low > 0.0f && open > 0.0f && close > 0.0f)
                {
                    float rsVariance = logf(high / open) * logf(high / close) +
                                      logf(low / open) * logf(low / close);
                    sumRSVariance += rsVariance;
                }
            }

            // Average the variance over the period, then convert to annualized percentage volatility
            float avgVariance = sumRSVariance / period;
            // Rogers-Satchell: Convert to annualized percentage volatility
            RSValues[calcIdx] = sqrtf(fabsf(avgVariance)) * 100.0f * sqrtf(252.0f);
        }
    }

    // 7. Parkinson Volatility
    if (UseParkinson.GetYesNo())
    {
        // Parkinson Volatility Estimator using ROLLING WINDOW of previous data
        if (calcIdx >= ParkinsonPeriod.GetInt())
        {
            float sumParkinsonVariance = 0.0f;
            int period = ParkinsonPeriod.GetInt();

            // Calculate Parkinson variance for each bar in the window
            for (int i = calcIdx - period + 1; i <= calcIdx; i++)
            {
                float high = sc.High[i];
                float low = sc.Low[i];

                if (high > 0.0f && low > 0.0f && high > low)
                {
                    float pVariance = (1.0f / (4.0f * logf(2.0f))) * powf(logf(high / low), 2.0f);
                    sumParkinsonVariance += pVariance;
                }
            }

            // Average the variance over the period, then convert to annualized percentage volatility
            float avgVariance = sumParkinsonVariance / period;
            // Parkinson: Convert to annualized percentage volatility
            ParkinsonValues[calcIdx] = sqrtf(avgVariance) * 100.0f * sqrtf(252.0f);
        }
    }

    // 8. Range Ratio
    if (UseRangeRatio.GetYesNo())
    {
        // Range Ratio using ROLLING WINDOW of previous data
        if (calcIdx >= RangeRatioPeriod.GetInt())
        {
            float sumRangeRatio = 0.0f;
            int period = RangeRatioPeriod.GetInt();

            // Calculate Range Ratio for each bar in the window
            for (int i = calcIdx - period + 1; i <= calcIdx; i++)
            {
                float high = sc.High[i];
                float low = sc.Low[i];
                float close = sc.Close[i];

                if (close > 0.0f)
                {
                    float rangeRatio = (high - low) / close;
                    sumRangeRatio += rangeRatio;
                }
            }

            // Average the range ratio over the period
            RangeRatioValues[calcIdx] = sumRangeRatio / period;
        }
    }

    // 9. Z-Score of Returns
    if (UseZScore.GetYesNo())
    {
        // Z-Score of Returns using PREVIOUS completed data
        if (calcIdx >= 1)
        {
            LogReturns[calcIdx] = CalculateLogReturn(sc.Close[calcIdx], sc.Close[calcIdx-1]);

            if (calcIdx >= ZScorePeriod.GetInt())
            {
                // Calculate mean and standard deviation of returns
                float sum = 0.0f;
                int period = ZScorePeriod.GetInt();

                for (int i = calcIdx - period + 1; i <= calcIdx; i++)
                {
                    sum += LogReturns[i];
                }
                float mean = sum / period;

                float sumSquaredDiff = 0.0f;
                for (int i = calcIdx - period + 1; i <= calcIdx; i++)
                {
                    float diff = LogReturns[i] - mean;
                    sumSquaredDiff += diff * diff;
                }
                float stdDev = sqrtf(sumSquaredDiff / period);

                if (stdDev > 0.0f)
                {
                    float zScore = (LogReturns[calcIdx] - mean) / stdDev;
                    ZScoreValues[calcIdx] = fabsf(zScore); // Use absolute value
                }
            }
        }
    }

    // 10. Realized Volatility
    if (UseRealizedVol.GetYesNo())
    {
        // Realized Volatility using PREVIOUS completed data
        if (calcIdx >= 1)
        {
            LogReturns[calcIdx] = CalculateLogReturn(sc.Close[calcIdx], sc.Close[calcIdx-1]);

            if (calcIdx >= RealizedVolPeriod.GetInt())
            {
                float sumSquaredReturns = 0.0f;
                int period = RealizedVolPeriod.GetInt();

                for (int i = calcIdx - period + 1; i <= calcIdx; i++)
                {
                    sumSquaredReturns += LogReturns[i] * LogReturns[i];
                }

                // Realized Volatility: Convert to annualized percentage volatility
                RealizedVolValues[calcIdx] = sqrtf(sumSquaredReturns / period) * 100.0f * sqrtf(252.0f);
            }
        }
    }

    // APPLY SMOOTHING TO ALL ACTIVE METHODS (if enabled)
    if (EnableSmoothing.GetYesNo())
    {
        int smoothPeriod = SmoothingPeriod.GetInt();
        if (UseATR.GetYesNo()) ApplySmoothing(ATRValues, ATRSmoothed, calcIdx, smoothPeriod);
        if (UseStdDev.GetYesNo()) ApplySmoothing(StdDevValues, StdDevSmoothed, calcIdx, smoothPeriod);
        if (UseEWMA.GetYesNo()) ApplySmoothing(EWMAValues, EWMASmoothed, calcIdx, smoothPeriod);
        if (UseYangZhang.GetYesNo()) ApplySmoothing(YZValues, YZSmoothed, calcIdx, smoothPeriod);
        if (UseGarmanKlass.GetYesNo()) ApplySmoothing(GKValues, GKSmoothed, calcIdx, smoothPeriod);
        if (UseRogersSatchell.GetYesNo()) ApplySmoothing(RSValues, RSSmoothed, calcIdx, smoothPeriod);
        if (UseParkinson.GetYesNo()) ApplySmoothing(ParkinsonValues, ParkinsonSmoothed, calcIdx, smoothPeriod);
        if (UseRangeRatio.GetYesNo()) ApplySmoothing(RangeRatioValues, RangeRatioSmoothed, calcIdx, smoothPeriod);
        if (UseZScore.GetYesNo()) ApplySmoothing(ZScoreValues, ZScoreSmoothed, calcIdx, smoothPeriod);
        if (UseRealizedVol.GetYesNo()) ApplySmoothing(RealizedVolValues, RealizedVolSmoothed, calcIdx, smoothPeriod);
    }

    // MULTI-VOL ENSEMBLE CALCULATION
    // Normalize each active method individually (using smoothed or raw values), then average

    float normalizedValue = 0.0f;
    int activeNormalizedMethods = 0;

    // OPTIMIZED: Use minimum window of 10 bars for faster response (was 20)
    int minWindow = (NormalizationWindow.GetInt() < 10) ? 10 : NormalizationWindow.GetInt();
    int actualWindow = (calcIdx >= minWindow) ? NormalizationWindow.GetInt() : (calcIdx + 1);

    if (calcIdx >= 9) // OPTIMIZED: Start normalizing after 10 bars (was 19)
    {
        // Normalize each active volatility method individually (smoothed or raw)
        if (UseATR.GetYesNo())
        {
            SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? ATRSmoothed : ATRValues;
            if (sourceArray[calcIdx] > 0.0f)
            {
                float atrNormalized = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                normalizedValue += atrNormalized;
                activeNormalizedMethods++;
            }
        }

        if (UseStdDev.GetYesNo())
        {
            SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? StdDevSmoothed : StdDevValues;
            if (sourceArray[calcIdx] > 0.0f)
            {
                float stdNormalized = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                normalizedValue += stdNormalized;
                activeNormalizedMethods++;
            }
        }

        if (UseEWMA.GetYesNo())
        {
            SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? EWMASmoothed : EWMAValues;
            if (sourceArray[calcIdx] > 0.0f)
            {
                float ewmaNormalized = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                normalizedValue += ewmaNormalized;
                activeNormalizedMethods++;
            }
        }

        if (UseYangZhang.GetYesNo())
        {
            SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? YZSmoothed : YZValues;
            if (sourceArray[calcIdx] > 0.0f)
            {
                float yzNormalized = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                normalizedValue += yzNormalized;
                activeNormalizedMethods++;
            }
        }

        if (UseGarmanKlass.GetYesNo())
        {
            SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? GKSmoothed : GKValues;
            if (sourceArray[calcIdx] > 0.0f)
            {
                float gkNormalized = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                normalizedValue += gkNormalized;
                activeNormalizedMethods++;
            }
        }

        if (UseRogersSatchell.GetYesNo())
        {
            SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? RSSmoothed : RSValues;
            if (sourceArray[calcIdx] > 0.0f)
            {
                float rsNormalized = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                normalizedValue += rsNormalized;
                activeNormalizedMethods++;
            }
        }

        if (UseParkinson.GetYesNo())
        {
            SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? ParkinsonSmoothed : ParkinsonValues;
            if (sourceArray[calcIdx] > 0.0f)
            {
                float parkinsonNormalized = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                normalizedValue += parkinsonNormalized;
                activeNormalizedMethods++;
            }
        }

        if (UseRangeRatio.GetYesNo())
        {
            SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? RangeRatioSmoothed : RangeRatioValues;
            if (sourceArray[calcIdx] > 0.0f)
            {
                float rangeNormalized = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                normalizedValue += rangeNormalized;
                activeNormalizedMethods++;
            }
        }

        if (UseZScore.GetYesNo())
        {
            SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? ZScoreSmoothed : ZScoreValues;
            if (sourceArray[calcIdx] > 0.0f)
            {
                float zscoreNormalized = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                normalizedValue += zscoreNormalized;
                activeNormalizedMethods++;
            }
        }

        if (UseRealizedVol.GetYesNo())
        {
            SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? RealizedVolSmoothed : RealizedVolValues;
            if (sourceArray[calcIdx] > 0.0f)
            {
                float realizedNormalized = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                normalizedValue += realizedNormalized;
                activeNormalizedMethods++;
            }
        }

        // Calculate ensemble average
        if (activeNormalizedMethods > 0)
        {
            normalizedValue = normalizedValue / activeNormalizedMethods;
            NormalizedValues[calcIdx] = normalizedValue;
        }
        else
        {
            // FALLBACK: If no methods have valid values, force ATR calculation for basic coloring
            if (UseATR.GetYesNo() && calcIdx >= 1)
            {
                float tr = CalculateTrueRange(sc.High[calcIdx], sc.Low[calcIdx], sc.Close[calcIdx-1]);
                if (tr > 0.0f)
                {
                    // Use simple ATR normalization as fallback
                    float simpleATR = tr / sc.Close[calcIdx]; // Simple percentage
                    normalizedValue = (simpleATR > 1.0f) ? 1.0f : simpleATR; // Clamp to 0-1
                }
                else
                {
                    normalizedValue = 0.1f; // Low volatility fallback
                }
            }
            else
            {
                // If no methods are active or have valid values, use low volatility color
                normalizedValue = 0.1f;
            }
        }
    }
    else
    {
        // For early bars (before bar 19), calculate simple volatility for coloring
        if (UseATR.GetYesNo() && calcIdx >= 1 && ATRValues[calcIdx] > 0.0f)
        {
            // Use simple ATR-based coloring for early bars
            float simpleVol = ATRValues[calcIdx] / sc.Close[calcIdx]; // ATR as percentage of price
            normalizedValue = (simpleVol > 0.05f) ? 0.8f : (simpleVol * 16.0f); // Scale 0-5% to 0-0.8
            if (normalizedValue > 1.0f) normalizedValue = 1.0f;
        }
        else
        {
            // For very early bars, use low volatility color
            normalizedValue = 0.2f;
        }
    }

    // VOLATILITY STATISTICS DISPLAY - Only update on new bars or setting changes
    static int lastStatsBarIndex = -1;
    bool newBarFormed = (idx != lastStatsBarIndex);
    bool shouldUpdateStats = newBarFormed || settingsChanged;

    if (ShowStatistics.GetYesNo() && shouldUpdateStats)
    {
        // Clear any existing text drawings for this study to prevent overlap
        sc.DeleteACSChartDrawing(sc.ChartNumber, TOOL_DELETE_ALL, 0);

        // Update the last bar index
        lastStatsBarIndex = idx;

        // Show stats for current candle - ALWAYS show if enabled (even with minimal data)
        if (calcIdx >= 0 && calcIdx < sc.ArraySize)
        {
            // OPTIMIZED: Use minimum window for normalization calculations (same as ensemble logic)
            int minWindow = (NormalizationWindow.GetInt() < 10) ? 10 : NormalizationWindow.GetInt();
            int actualWindow = (calcIdx >= minWindow) ? NormalizationWindow.GetInt() : (calcIdx + 1);

            // Build the statistics display
            SCString headerText = "VOLATILITY STATISTICS";
            SCString statsText = "";
            bool firstMethod = true;

            // ALWAYS show header, even if no data yet
            // Display each active method's raw value and normalized value
            if (UseATR.GetYesNo() && calcIdx < sc.ArraySize)
            {
                SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? ATRSmoothed : ATRValues;
                float rawValue = sourceArray[calcIdx];
                if (rawValue > 0.0f)
                {
                    float normValue = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                    if (!firstMethod) statsText += "\n";
                    statsText.Format("%sATR: %.4f - %.3f", statsText.GetChars(), rawValue, normValue);
                    firstMethod = false;
                }
                else if (calcIdx >= 1) // Show "calculating" for early bars
                {
                    if (!firstMethod) statsText += "\n";
                    statsText.Format("%sATR: Calculating...", statsText.GetChars());
                    firstMethod = false;
                }
            }

            if (UseStdDev.GetYesNo() && StdDevValues[calcIdx] > 0.0f)
            {
                SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? StdDevSmoothed : StdDevValues;
                float rawValue = sourceArray[calcIdx];
                float normValue = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                if (!firstMethod) statsText += "\n";
                statsText.Format("%sStdDev: %.4f - %.3f", statsText.GetChars(), rawValue, normValue);
                firstMethod = false;
            }

            if (UseEWMA.GetYesNo() && EWMAValues[calcIdx] > 0.0f)
            {
                SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? EWMASmoothed : EWMAValues;
                float rawValue = sourceArray[calcIdx];
                float normValue = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                if (!firstMethod) statsText += "\n";
                statsText.Format("%sEWMA: %.4f - %.3f", statsText.GetChars(), rawValue, normValue);
                firstMethod = false;
            }

            if (UseYangZhang.GetYesNo() && YZValues[calcIdx] > 0.0f)
            {
                SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? YZSmoothed : YZValues;
                float rawValue = sourceArray[calcIdx];
                float normValue = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                if (!firstMethod) statsText += "\n";
                statsText.Format("%sYZ: %.4f - %.3f", statsText.GetChars(), rawValue, normValue);
                firstMethod = false;
            }

            if (UseGarmanKlass.GetYesNo() && GKValues[calcIdx] > 0.0f)
            {
                SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? GKSmoothed : GKValues;
                float rawValue = sourceArray[calcIdx];
                float normValue = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                if (!firstMethod) statsText += "\n";
                statsText.Format("%sGK: %.4f - %.3f", statsText.GetChars(), rawValue, normValue);
                firstMethod = false;
            }

            if (UseRogersSatchell.GetYesNo() && RSValues[calcIdx] > 0.0f)
            {
                SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? RSSmoothed : RSValues;
                float rawValue = sourceArray[calcIdx];
                float normValue = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                if (!firstMethod) statsText += "\n";
                statsText.Format("%sRS: %.4f - %.3f", statsText.GetChars(), rawValue, normValue);
                firstMethod = false;
            }

            if (UseParkinson.GetYesNo() && ParkinsonValues[calcIdx] > 0.0f)
            {
                // TEMP FIX: Always use raw values for display to debug the issue
                float rawValue = ParkinsonValues[calcIdx];
                SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? ParkinsonSmoothed : ParkinsonValues;
                float normValue = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                if (!firstMethod) statsText += "\n";
                statsText.Format("%sParkinson: %.4f - %.3f", statsText.GetChars(), rawValue, normValue);
                firstMethod = false;
            }

            if (UseRangeRatio.GetYesNo() && RangeRatioValues[calcIdx] > 0.0f)
            {
                // TEMP FIX: Always use raw values for display to debug the issue
                float rawValue = RangeRatioValues[calcIdx];
                SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? RangeRatioSmoothed : RangeRatioValues;
                float normValue = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                if (!firstMethod) statsText += "\n";
                statsText.Format("%sRange: %.4f - %.3f", statsText.GetChars(), rawValue, normValue);
                firstMethod = false;
            }

            if (UseZScore.GetYesNo() && ZScoreValues[calcIdx] > 0.0f)
            {
                // TEMP FIX: Always use raw values for display to debug the issue
                float rawValue = ZScoreValues[calcIdx];
                SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? ZScoreSmoothed : ZScoreValues;
                float normValue = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                if (!firstMethod) statsText += "\n";
                statsText.Format("%sZ-Score: %.4f - %.3f", statsText.GetChars(), rawValue, normValue);
                firstMethod = false;
            }

            if (UseRealizedVol.GetYesNo() && RealizedVolValues[calcIdx] > 0.0f)
            {
                // TEMP FIX: Always use raw values for display to debug the issue
                float rawValue = RealizedVolValues[calcIdx];
                SCFloatArrayRef sourceArray = EnableSmoothing.GetYesNo() ? RealizedVolSmoothed : RealizedVolValues;
                float normValue = NormalizeValue(sc, sourceArray, calcIdx, actualWindow, SoftClipPercent.GetFloat(), sc.ArraySize);
                if (!firstMethod) statsText += "\n";
                statsText.Format("%sRealized: %.4f - %.3f", statsText.GetChars(), rawValue, normValue);
                firstMethod = false;
            }

            // Add ensemble average if multi-vol is enabled and multiple methods are active
            if (EnableMultiVol.GetYesNo() && activeNormalizedMethods > 1)
            {
                if (!firstMethod) statsText += "\n";
                statsText.Format("%sEnsemble: %.3f", statsText.GetChars(), normalizedValue);
            }

            // ALWAYS display header, add stats if available, or show status
            SCString combinedText = headerText;
            if (statsText.GetLength() > 0)
            {
                combinedText += "\n";
                combinedText += statsText;
            }
            else
            {
                // Show status if no stats available yet
                combinedText += "\n";
                combinedText += "Loading volatility data...";
            }

                // Position above COMPLETED bar's closing price using user-defined distance (centered over completed bar)
                float completedBarPrice = sc.Close[calcIdx]; // Use completed bar's close, not current real-time price
                int userDistance = StatsDistanceAbovePrice.GetInt();
                float displayPrice = completedBarPrice + (userDistance * sc.TickSize); // User-defined points above completed bar

                // Use text drawing positioned relative to price with gray color - CENTERED over completed bar
                s_UseTool Tool;
                Tool.Clear();
                Tool.ChartNumber = sc.ChartNumber;
                Tool.DrawingType = DRAWING_TEXT;
                Tool.LineNumber = 999999; // High number to avoid conflicts
                Tool.BeginIndex = calcIdx; // Use completed bar index - only updates on new bars
                Tool.BeginValue = displayPrice;
                Tool.Text = combinedText;
                Tool.Color = RGB(128, 128, 128); // Gray color
                Tool.FontBold = 1;
                Tool.FontSize = 12; // Larger font for visibility
                Tool.TextAlignment = DT_CENTER | DT_TOP; // CENTER horizontally over current bar
                Tool.AddMethod = UTAM_ADD_OR_ADJUST;
                Tool.ReverseTextColor = 0;
                Tool.TransparencyLevel = 0; // Fully opaque

                sc.UseTool(Tool);
        } // End of inner if (calcIdx >= 0 && calcIdx < sc.ArraySize)
    } // End of if (ShowStatistics.GetYesNo())

    // ABSOLUTE SAFETY: Ensure normalizedValue is always valid
    if (normalizedValue < 0.0f || normalizedValue > 1.0f || normalizedValue != normalizedValue) // Check for NaN
    {
        normalizedValue = 0.2f; // Force low volatility if invalid
    }

    // Get the appropriate color for this volatility level
    COLORREF volColor = GetVolatilityColor(normalizedValue);

    // TRIPLE SAFETY CHECK: Ensure color is never transparent/invalid
    if (volColor == 0 || volColor == RGB(0,0,0))
    {
        volColor = RGB(0, 0, 139); // Force deep blue if color is invalid
    }

    // CRITICAL: Apply color to CURRENT bar (idx) based on PREVIOUS data (calcIdx)
    // This ensures the color appears immediately when the candle opens, not after it closes
    sc.Subgraph[0].DataColor[idx] = volColor;

    // Store the normalized value for reference (optional)
    sc.Subgraph[0][idx] = normalizedValue;
} // End of main function
