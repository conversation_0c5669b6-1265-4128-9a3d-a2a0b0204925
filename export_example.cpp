// export.cpp
#include "sierrachart.h"
#include <cstdio>
#include <cmath>

SCDLLName("Levels Exporter")

// ---- Helpers ----
static bool ReadSubgraphLatestNonNaN(SCStudyInterfaceRef sc, int StudyID, int SubgraphIndex, double& outVal)
{
    outVal = NAN;
    if (StudyID <= 0 || SubgraphIndex < 0)
        return false;

    SCFloatArray arr;
    sc.GetStudyArrayUsingID(StudyID, SubgraphIndex, arr); // (studyID, subgraphIndex, outArray)

    const int n = arr.GetArraySize();
    if (n <= 0) return false;

    for (int i = n - 1; i >= 0; --i)
    {
        const float v = arr[i];
        if (!std::isnan(v))
        {
            outVal = sc.RoundToTickSize(v, sc.TickSize);
            return true;
        }
    }
    return false;
}

// Collect peaks or valleys from the most recent profile.
// typeWanted: 1=Peak (HVN), 2=Valley (LVN). Returns count; writes unique (tick-rounded) values into outPrices.
static int CollectAllPV(SCStudyInterfaceRef sc, int StudyID, int typeWanted, double* outPrices, int outCap)
{
    if (StudyID <= 0 || outCap <= 0) return 0;

    const int ProfileIndex = -1; // rightmost profile
    int written = 0;

    // de-dup store
    double seen[512];
    int seenN = 0;
    auto alreadySeen = [&](double y)->bool {
        for (int i = 0; i < seenN; ++i) if (seen[i] == y) return true;
        return false;
    };
    auto pushSeen = [&](double y){ if (seenN < 512) seen[seenN++] = y; };

    for (int pvIdx = 0; pvIdx < 1024 && written < outCap; ++pvIdx)
    {
        float price = 0.0f;
        int pvType = 0;
        int startIdx = 0, endIdx = 0;

        const int ok = sc.GetStudyPeakValleyLine(sc.ChartNumber, StudyID, price, pvType, startIdx, endIdx, ProfileIndex, pvIdx);
        if (!ok || pvType == 0)
            break;

        if (pvType == typeWanted)
        {
            double y = sc.RoundToTickSize(price, sc.TickSize);
            if (y == 0.0) continue;
            if (alreadySeen(y)) continue;

            if (written < outCap) outPrices[written] = y;
            ++written;
            pushSeen(y);
        }
    }
    return written;
}


static void SortByAbsDistance(double* arr, int n, double ref)
{
    for (int i = 0; i < n - 1; ++i)
    {
        int minIdx = i;
        double minDist = fabs(arr[i] - ref);
        for (int j = i + 1; j < n; ++j)
        {
            double d = fabs(arr[j] - ref);
            if (d < minDist) { minDist = d; minIdx = j; }
        }
        if (minIdx != i) { double tmp = arr[i]; arr[i] = arr[minIdx]; arr[minIdx] = tmp; }
    }
}


static bool GetTodaysOpenApprox(SCStudyInterfaceRef sc, double& outOpen)
{
    const int last = sc.ArraySize - 1;
    if (last < 0) return false;

    const int lastDate = sc.BaseDateTimeIn[last].GetDate();

    int firstIdx = last;
    while (firstIdx > 0 && sc.BaseDateTimeIn[firstIdx - 1].GetDate() == lastDate)
        --firstIdx;

    if (firstIdx >= 0 && firstIdx <= last)
    {
        outOpen = sc.RoundToTickSize(sc.Open[firstIdx], sc.TickSize);
        return true;
    }
    return false;
}

SCSFExport scsf_LevelsExporter_Filtered(SCStudyInterfaceRef sc)
{
    // Outputs & Debug
    SCInputRef In_OutputFile = sc.Input[0];   // filename only; saved to Data folder
    SCInputRef In_Debug      = sc.Input[1];   // Yes/No logs

    //Purple (HVNs)-
    SCInputRef StudyP  = sc.Input[10]; // Study ID (same chart)
    SCInputRef MaxP    = sc.Input[11]; // Max HVNs when filter is OFF
    SCInputRef TagP    = sc.Input[12]; // tag string

    // Yellow (LVNs)
    SCInputRef StudyY  = sc.Input[20];
    SCInputRef MaxY    = sc.Input[21]; // Max LVNs when filter is OFF
    SCInputRef TagY    = sc.Input[22];

    //Red (POC, single)
    SCInputRef StudyR  = sc.Input[30];
    SCInputRef TagR    = sc.Input[31];

    // Filter around reference price day open or manual
    SCInputRef UseFilter      = sc.Input[40]; // toggle
    SCInputRef RefMode        = sc.Input[41]; // 0 = Today Open (auto), 1 = Manual Price
    SCInputRef ManualRefPrice = sc.Input[42]; // used if RefMode=1

    // per-row limits when filter ON
    SCInputRef P_MaxAbove     = sc.Input[43];
    SCInputRef P_MaxBelow     = sc.Input[44];
    SCInputRef Y_MaxAbove     = sc.Input[45];
    SCInputRef Y_MaxBelow     = sc.Input[46];

    // Persist last written content
    SCString& prevKey = sc.GetPersistentSCString(1);

    if (sc.SetDefaults)
    {
        sc.GraphName        = "Levels Exporter)";
        sc.StudyDescription = "Exports 3 rows (purple/yellow/red). Optional filter around day open: include X above / Y below.";
        sc.AutoLoop         = 0;

        In_OutputFile.Name  = "Output CSV File (under Data folder)";
        In_OutputFile.SetString("LevelsExport.csv");

        In_Debug.Name       = "Debug logging to Message Log";
        In_Debug.SetYesNo(0);

        // Purple defaults (HVNs)
        StudyP.Name = "Row 1 (purple HVN): Source Study (ID)";
        StudyP.SetStudyID(8); // set to your VbP Study ID
        MaxP.Name   = "Row 1 (purple HVN): Max Peaks (when filter OFF)";
        MaxP.SetInt(5);
        TagP.Name   = "Row 1 (purple HVN): Tag";
        TagP.SetString("purple");

        // Yellow defaults (LVNs)
        StudyY.Name = "Row 2 (yellow LVN): Source Study (ID)";
        StudyY.SetStudyID(8);
        MaxY.Name   = "Row 2 (yellow LVN): Max Valleys (when filter OFF)";
        MaxY.SetInt(5);
        TagY.Name   = "Row 2 (yellow LVN): Tag";
        TagY.SetString("yellow");

        // Red defaults (POC)
        StudyR.Name = "Row 3 (red POC): Source Study (ID)";
        StudyR.SetStudyID(8);
        TagR.Name   = "Row 3 (red POC): Tag";
        TagR.SetString("red");

        // Filter controls
        UseFilter.Name      = "Filter around reference price (toggle)";
        UseFilter.SetYesNo(0);

        RefMode.Name        = "Reference Source (when filter ON)";
        RefMode.SetCustomInputStrings("Today Open (auto);Manual Price");
        RefMode.SetCustomInputIndex(0);

        ManualRefPrice.Name = "Manual Reference Price (used if Reference=Manual)";
        ManualRefPrice.SetFloat(0.0f);

        P_MaxAbove.Name     = "Purple HVN: Max levels ABOVE reference (when filter ON)";
        P_MaxAbove.SetInt(2);
        P_MaxBelow.Name     = "Purple HVN: Max levels BELOW reference (when filter ON)";
        P_MaxBelow.SetInt(2);

        Y_MaxAbove.Name     = "Yellow LVN: Max levels ABOVE reference (when filter ON)";
        Y_MaxAbove.SetInt(2);
        Y_MaxBelow.Name     = "Yellow LVN: Max levels BELOW reference (when filter ON)";
        Y_MaxBelow.SetInt(2);

        prevKey = "";
        return;
    }

    //Resolve reference price if filtering
    const bool filtering = UseFilter.GetYesNo() != 0;
    double refPrice = NAN;

    if (filtering)
    {
        if (RefMode.GetIndex() == 0) // Today Open (auto)
        {
            if (!GetTodaysOpenApprox(sc, refPrice))
            {
                refPrice = ManualRefPrice.GetFloat();
            }
        }
        else
        {
            refPrice = ManualRefPrice.GetFloat();
        }
    }

    //HVNs and LVNs
    double peaksAll[256];  int nPeaksAll  = CollectAllPV(sc, StudyP.GetStudyID(), 1 /*Peak*/,   peaksAll, 256);
    double valleysAll[256];int nValleysAll= CollectAllPV(sc, StudyY.GetStudyID(), 2 /*Valley*/, valleysAll, 256);

    /POC (single via SG2)
    double vPOC = NAN;
    bool hasPOC = ReadSubgraphLatestNonNaN(sc, StudyR.GetStudyID(), 2, vPOC);

    // ---- Apply filter (if enabled), else limit to MaxX ----
    double peaksOut[256];   int nPeaksOut   = 0;
    double valleysOut[256]; int nValleysOut = 0;

    if (filtering && !std::isnan(refPrice))
    {
        // Split into above/below and sort each by closeness to reference
        double above[256]; int na = 0;
        double below[256]; int nb = 0;

        for (int i = 0; i < nPeaksAll; ++i)
        {
            if (peaksAll[i] > refPrice) above[na++] = peaksAll[i];
            else if (peaksAll[i] < refPrice) below[nb++] = peaksAll[i];
        }
        SortByAbsDistance(above, na, refPrice);
        SortByAbsDistance(below, nb, refPrice);

        const int wantAbove = P_MaxAbove.GetInt() > 0 ? P_MaxAbove.GetInt() : 0;
        const int wantBelow = P_MaxBelow.GetInt() > 0 ? P_MaxBelow.GetInt() : 0;

        int w = 0;
        for (int i = 0; i < na && w < wantAbove; ++i) peaksOut[w++] = above[i];
        for (int i = 0; i < nb && (w - wantAbove) < wantBelow; ++i) peaksOut[w++] = below[i];
        nPeaksOut = w;

        // LVNs
        na = nb = 0;
        for (int i = 0; i < nValleysAll; ++i)
        {
            if (valleysAll[i] > refPrice) above[na++] = valleysAll[i];
            else if (valleysAll[i] < refPrice) below[nb++] = valleysAll[i];
        }
        SortByAbsDistance(above, na, refPrice);
        SortByAbsDistance(below, nb, refPrice);

        const int wantAboveY = Y_MaxAbove.GetInt() > 0 ? Y_MaxAbove.GetInt() : 0;
        const int wantBelowY = Y_MaxBelow.GetInt() > 0 ? Y_MaxBelow.GetInt() : 0;

        w = 0;
        for (int i = 0; i < na && w < wantAboveY; ++i) valleysOut[w++] = above[i];
        for (int i = 0; i < nb && (w - wantAboveY) < wantBelowY; ++i) valleysOut[w++] = below[i];
        nValleysOut = w;
    }
    else
    {
        // No filter
        int capP = MaxP.GetInt(); if (capP < 0) capP = 0;
        int capY = MaxY.GetInt(); if (capY < 0) capY = 0;

        nPeaksOut = (nPeaksAll < capP) ? nPeaksAll : capP;
        for (int i = 0; i < nPeaksOut; ++i) peaksOut[i] = peaksAll[i];

        nValleysOut = (nValleysAll < capY) ? nValleysAll : capY;
        for (int i = 0; i < nValleysOut; ++i) valleysOut[i] = valleysAll[i];
    }

    // Build CSV with 3 fixed rows (purple / yellow / red)
    SCString tagP = TagP.GetString(); if (tagP.IsEmpty()) tagP = "purple";
    SCString tagY = TagY.GetString(); if (tagY.IsEmpty()) tagY = "yellow";
    SCString tagR = TagR.GetString(); if (tagR.IsEmpty()) tagR = "red";

    SCString csv;

    // Purple row
    csv += tagP; csv += ",";
    for (int i = 0; i < nPeaksOut; ++i) {
        if (i > 0) csv += ",";
        SCString tmp; tmp.Format("%.8f", peaksOut[i]); csv += tmp;
    }
    csv += "\n";

    // Yellow row
    csv += tagY; csv += ",";
    for (int i = 0; i < nValleysOut; ++i) {
        if (i > 0) csv += ",";
        SCString tmp; tmp.Format("%.8f", valleysOut[i]); csv += tmp;
    }
    csv += "\n";

    // Red row
    csv += tagR; csv += ",";
    if (!std::isnan(vPOC)) { SCString tmp; tmp.Format("%.8f", vPOC); csv += tmp; }
    csv += "\n";

    // ---- Change detection & write ----
    SCString key = csv;
    bool changed = (key != prevKey);

    if (changed)
    {
        SCString fileName = In_OutputFile.GetString();
        if (fileName.IsEmpty()) fileName = "LevelsExport.csv";

        SCString path = sc.DataFilesFolder();
        path += "\\";
        path += fileName;

        FILE* f = fopen(path.GetChars(), "wb");
        if (f) {
            fwrite(csv.GetChars(), 1, (size_t)csv.GetLength(), f);
            fclose(f);
        }

        if (In_Debug.GetYesNo())
        {
            SCString msg, refStr, pocStr;

            // ref string
            if (filtering && !std::isnan(refPrice))
                refStr.Format("%.8f", refPrice);
            else
                refStr = "(n/a)";

            msg.Format("Ref %s: %s", filtering ? "ON" : "OFF", refStr.GetChars());
            sc.AddMessageToLog(msg, 0);

            // POC string
            if (!std::isnan(vPOC))
                pocStr.Format("%.8f", vPOC);
            else
                pocStr = "(blank)";

            msg.Format("HVNs out: %d | LVNs out: %d | POC: %s",
                       nPeaksOut, nValleysOut, pocStr.GetChars());
            sc.AddMessageToLog(msg, 0);
        }

        prevKey = key;
    }
}
