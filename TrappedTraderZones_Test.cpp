#include "sierrachart.h"

SCDLLName("Trapped Trader Zones Test")

/*==========================================================================*/
SCSFExport scsf_TrappedTraderZonesTest(SCStudyInterfaceRef sc)
{
    SCSubgraphRef TestOutput = sc.Subgraph[0];
    SCSubgraphRef VolumeInfo = sc.Subgraph[1];
    
    if (sc.SetDefaults)
    {
        sc.GraphName = "Trapped Trader Zones Test";
        sc.StudyDescription = "Test utility to verify volume at price data availability and basic functionality";
        
        sc.AutoLoop = 1;
        sc.GraphRegion = 1;
        sc.ValueFormat = 0;
        
        TestOutput.Name = "Test Status";
        TestOutput.DrawStyle = DRAWSTYLE_LINE;
        TestOutput.PrimaryColor = RGB(0, 255, 0);
        
        VolumeInfo.Name = "Volume Info";
        VolumeInfo.DrawStyle = DRAWSTYLE_IGNORE;
        
        return;
    }
    
    // Test 1: Check if Volume at Price data is available
    if (sc.VolumeAtPriceForBars == nullptr)
    {
        TestOutput[sc.Index] = 0;  // Failed
        if (sc.Index == sc.ArraySize - 1)  // Only log once at the end
        {
            sc.AddMessageToLog("TEST FAILED: Volume at Price data not available. Enable 'Intraday Data Storage Time Unit' in Chart Settings.", 1);
        }
        return;
    }
    
    // Test 2: Check if we can access volume data for current bar
    if (sc.Index < 1)
    {
        TestOutput[sc.Index] = 1;  // Passed basic test
        return;
    }
    
    float BarHigh = sc.High[sc.Index];
    float BarLow = sc.Low[sc.Index];
    
    // Convert to ticks
    int HighTick = sc.PriceValueToTicks(BarHigh);
    int LowTick = sc.PriceValueToTicks(BarLow);
    
    unsigned int TotalBidVolume = 0;
    unsigned int TotalAskVolume = 0;
    unsigned int TotalVolume = 0;
    int PriceLevelsWithData = 0;
    
    // Test 3: Analyze volume at price for current bar
    for (int PriceTick = LowTick; PriceTick <= HighTick; PriceTick++)
    {
        unsigned int BidVol = sc.VolumeAtPriceForBars->GetBidVolumeAtPrice(sc.Index, PriceTick);
        unsigned int AskVol = sc.VolumeAtPriceForBars->GetAskVolumeAtPrice(sc.Index, PriceTick);
        unsigned int Vol = sc.VolumeAtPriceForBars->GetVolumeAtPrice(sc.Index, PriceTick);
        
        TotalBidVolume += BidVol;
        TotalAskVolume += AskVol;
        TotalVolume += Vol;
        
        if (BidVol > 0 || AskVol > 0)
            PriceLevelsWithData++;
    }
    
    // Test 4: Validate volume data
    if (TotalVolume > 0 && (TotalBidVolume > 0 || TotalAskVolume > 0))
    {
        TestOutput[sc.Index] = 2;  // Passed volume data test
        VolumeInfo[sc.Index] = (float)TotalVolume;
        
        // Log detailed info every 100 bars
        if (sc.Index % 100 == 0)
        {
            SCString Message;
            Message.Format("Bar %d: Total Vol=%u, Bid=%u, Ask=%u, Price Levels=%d", 
                          sc.Index, TotalVolume, TotalBidVolume, TotalAskVolume, PriceLevelsWithData);
            sc.AddMessageToLog(Message, 0);
        }
    }
    else
    {
        TestOutput[sc.Index] = 1;  // Basic test passed but no volume data
    }
    
    // Test 5: Check for aggressive volume patterns
    if (TotalBidVolume > 0 && TotalAskVolume > 0)
    {
        float BidAskRatio = (float)TotalBidVolume / (float)TotalAskVolume;
        float AskBidRatio = (float)TotalAskVolume / (float)TotalBidVolume;
        
        // Check for significant imbalance (2.5:1 ratio)
        if (BidAskRatio >= 2.5f || AskBidRatio >= 2.5f)
        {
            TestOutput[sc.Index] = 3;  // Found aggressive pattern
            
            SCString Message;
            Message.Format("Aggressive pattern at bar %d: Bid=%u, Ask=%u, Ratio=%.2f", 
                          sc.Index, TotalBidVolume, TotalAskVolume, 
                          BidAskRatio > AskBidRatio ? BidAskRatio : AskBidRatio);
            sc.AddMessageToLog(Message, 0);
        }
    }
    
    // Final status report on last bar
    if (sc.Index == sc.ArraySize - 1)
    {
        SCString FinalMessage;
        if (TestOutput[sc.Index] >= 2)
        {
            FinalMessage = "TEST PASSED: Volume at Price data is available and functional for Trapped Trader Zones indicator.";
        }
        else if (TestOutput[sc.Index] == 1)
        {
            FinalMessage = "TEST PARTIAL: Volume at Price structure exists but no bid/ask data found. Check data feed settings.";
        }
        else
        {
            FinalMessage = "TEST FAILED: Volume at Price data not available. Check Chart Settings.";
        }
        
        sc.AddMessageToLog(FinalMessage, 1);
    }
}

/*==========================================================================*/
// Additional test function for Time and Sales data
SCSFExport scsf_TimeAndSalesTest(SCStudyInterfaceRef sc)
{
    SCSubgraphRef TSTest = sc.Subgraph[0];
    
    if (sc.SetDefaults)
    {
        sc.GraphName = "Time and Sales Test";
        sc.StudyDescription = "Test Time and Sales data availability";
        
        sc.AutoLoop = 0;  // Manual loop for T&S analysis
        sc.GraphRegion = 1;
        
        TSTest.Name = "T&S Status";
        TSTest.DrawStyle = DRAWSTYLE_LINE;
        TSTest.PrimaryColor = RGB(255, 255, 0);
        
        return;
    }
    
    // Get Time and Sales data
    c_SCTimeAndSalesArray TimeAndSalesArray;
    sc.GetTimeAndSales(TimeAndSalesArray);
    
    int TSSize = TimeAndSalesArray.Size();
    
    if (TSSize > 0)
    {
        int BidTrades = 0;
        int AskTrades = 0;
        int TotalTrades = 0;
        
        // Analyze last 100 T&S records
        int StartIndex = max(0, TSSize - 100);
        
        for (int i = StartIndex; i < TSSize; i++)
        {
            const s_TimeAndSales& TSRecord = TimeAndSalesArray[i];
            
            if (TSRecord.Type == SC_TS_BID)
                BidTrades++;
            else if (TSRecord.Type == SC_TS_ASK)
                AskTrades++;
            
            TotalTrades++;
        }
        
        SCString Message;
        Message.Format("Time & Sales: Total=%d, Bid Trades=%d, Ask Trades=%d", 
                      TotalTrades, BidTrades, AskTrades);
        sc.AddMessageToLog(Message, 0);
        
        // Set output value
        for (int i = 0; i < sc.ArraySize; i++)
        {
            TSTest[i] = (float)TSSize;
        }
    }
    else
    {
        sc.AddMessageToLog("No Time and Sales data available", 1);
        
        for (int i = 0; i < sc.ArraySize; i++)
        {
            TSTest[i] = 0;
        }
    }
}
