#include "sierrachart.h"

SCDLLName("Delta Heatmap Smooth Study")

// Function declaration
COLORREF LerpColor(COLORREF color1, COLORREF color2, float t);

/*==========================================================================*/
SCSFExport scsf_DeltaHeatmapSmooth(SCStudyInterfaceRef sc)
{
    // Input references
    SCInputRef Input_Smoothing = sc.Input[0];
    SCInputRef Input_GradientScale = sc.Input[1];
    SCInputRef Input_StrongBuyColor = sc.Input[2];
    SCInputRef Input_StrongSellColor = sc.Input[3];
    SCInputRef Input_NeutralColor = sc.Input[4];
    SCInputRef Input_CandleStyle = sc.Input[5];

    // Subgraph references for internal calculations
    SCSubgraphRef Subgraph_SmoothedDelta = sc.Subgraph[0];
    SCSubgraphRef Subgraph_AvgAbsDelta = sc.Subgraph[1];

    // Heiken Ashi calculation subgraphs (hidden)
    SCSubgraphRef Subgraph_HAOpen = sc.Subgraph[2];
    SCSubgraphRef Subgraph_HAHigh = sc.Subgraph[3];
    SCSubgraphRef Subgraph_HALow = sc.Subgraph[4];
    SCSubgraphRef Subgraph_HAClose = sc.Subgraph[5];

    // Visual candle subgraphs
    SCSubgraphRef Subgraph_CandleOpen = sc.Subgraph[6];
    SCSubgraphRef Subgraph_CandleHigh = sc.Subgraph[7];
    SCSubgraphRef Subgraph_CandleLow = sc.Subgraph[8];
    SCSubgraphRef Subgraph_CandleClose = sc.Subgraph[9];

    if (sc.SetDefaults)
    {
        // Study configuration
        sc.GraphName = "Delta Heatmap Smooth";
        sc.StudyDescription = "Heatmap overlay that colors candle bodies, borders and shadows based on buy/sell pressure using smoothed delta values";
        sc.GraphRegion = 0; // Main price region
        sc.AutoLoop = 1;    // Enable auto-looping
        sc.UpdateAlways = 1; // Update on every tick

        // Input parameters
        Input_Smoothing.Name = "Lookback Smoothing (bars)";
        Input_Smoothing.SetInt(20);
        Input_Smoothing.SetIntLimits(1, 500);

        Input_GradientScale.Name = "Gradient Scale (x Avg|Delta|)";
        Input_GradientScale.SetFloat(2.0f);
        Input_GradientScale.SetFloatLimits(0.1f, 10.0f);

        Input_StrongBuyColor.Name = "Strong Buy Color";
        Input_StrongBuyColor.SetColor(RGB(0, 179, 255));

        Input_StrongSellColor.Name = "Strong Sell Color";
        Input_StrongSellColor.SetColor(RGB(255, 0, 213));

        Input_NeutralColor.Name = "Neutral Color";
        Input_NeutralColor.SetColor(RGB(128, 128, 128));

        Input_CandleStyle.Name = "Candle Style";
        Input_CandleStyle.SetCustomInputStrings("Regular OHLC;Heiken Ashi");
        Input_CandleStyle.SetCustomInputIndex(0);

        // Subgraphs for calculations (not displayed)
        Subgraph_SmoothedDelta.Name = "Smoothed Delta";
        Subgraph_SmoothedDelta.DrawStyle = DRAWSTYLE_IGNORE;

        Subgraph_AvgAbsDelta.Name = "Average Absolute Delta";
        Subgraph_AvgAbsDelta.DrawStyle = DRAWSTYLE_IGNORE;

        // Heiken Ashi calculation subgraphs (hidden)
        Subgraph_HAOpen.Name = "HA Open";
        Subgraph_HAOpen.DrawStyle = DRAWSTYLE_IGNORE;
        Subgraph_HAHigh.Name = "HA High";
        Subgraph_HAHigh.DrawStyle = DRAWSTYLE_IGNORE;
        Subgraph_HALow.Name = "HA Low";
        Subgraph_HALow.DrawStyle = DRAWSTYLE_IGNORE;
        Subgraph_HAClose.Name = "HA Close";
        Subgraph_HAClose.DrawStyle = DRAWSTYLE_IGNORE;

        // Visual candle subgraphs
        Subgraph_CandleOpen.Name = "Open";
        Subgraph_CandleOpen.DrawStyle = DRAWSTYLE_IGNORE;
        Subgraph_CandleHigh.Name = "High";
        Subgraph_CandleHigh.DrawStyle = DRAWSTYLE_IGNORE;
        Subgraph_CandleLow.Name = "Low";
        Subgraph_CandleLow.DrawStyle = DRAWSTYLE_IGNORE;
        Subgraph_CandleClose.Name = "Close";
        Subgraph_CandleClose.DrawStyle = DRAWSTYLE_IGNORE;

        return;
    }

    // Get bid/ask volume data
    float bidVolume = sc.BidVolume[sc.Index];
    float askVolume = sc.AskVolume[sc.Index];
    
    // If no bid/ask data available, try to use NumberOfTrades as fallback
    if (bidVolume == 0 && askVolume == 0)
    {
        return; // No volume data available
    }
    
    double currentDelta = bidVolume - askVolume;

    // Calculate weighted smoothed delta
    double smoothedDelta = 0.0;
    double weightSum = 0.0;
    int smoothingPeriod = Input_Smoothing.GetInt();

    for (int i = 0; i < smoothingPeriod && sc.Index - i >= 0; i++)
    {
        // Get delta for bar at index (sc.Index - i)
        int barIndex = sc.Index - i;
        double barDelta = sc.BidVolume[barIndex] - sc.AskVolume[barIndex];

        // Weight calculation (more recent bars have higher weight)
        double weight = smoothingPeriod - i;
        smoothedDelta += barDelta * weight;
        weightSum += weight;
    }

    if (weightSum > 0)
        smoothedDelta /= weightSum;

    Subgraph_SmoothedDelta[sc.Index] = (float)smoothedDelta;

    // Calculate average absolute delta
    double avgAbsDelta = 0.0;
    int validBars = 0;

    for (int i = 0; i < smoothingPeriod && sc.Index - i >= 0; i++)
    {
        int barIndex = sc.Index - i;
        double barDelta = sc.BidVolume[barIndex] - sc.AskVolume[barIndex];
        
        avgAbsDelta += abs(barDelta);
        validBars++;
    }

    if (validBars > 0)
        avgAbsDelta /= validBars;
    else
        avgAbsDelta = 1.0; // Default value to avoid division by zero

    Subgraph_AvgAbsDelta[sc.Index] = (float)avgAbsDelta;

    // Calculate Heiken Ashi values
    float haOpen, haHigh, haLow, haClose;

    if (sc.Index == 0)
    {
        // First bar - use regular OHLC
        haOpen = sc.Open[0];
        haClose = (sc.Open[0] + sc.High[0] + sc.Low[0] + sc.Close[0]) / 4.0f;
    }
    else
    {
        // Subsequent bars
        haOpen = (Subgraph_HAOpen[sc.Index - 1] + Subgraph_HAClose[sc.Index - 1]) / 2.0f;
        haClose = (sc.Open[sc.Index] + sc.High[sc.Index] + sc.Low[sc.Index] + sc.Close[sc.Index]) / 4.0f;
    }

    haHigh = max(sc.High[sc.Index], max(haOpen, haClose));
    haLow = min(sc.Low[sc.Index], min(haOpen, haClose));

    // Store Heiken Ashi values
    Subgraph_HAOpen[sc.Index] = haOpen;
    Subgraph_HAHigh[sc.Index] = haHigh;
    Subgraph_HALow[sc.Index] = haLow;
    Subgraph_HAClose[sc.Index] = haClose;

    // Set candle values based on selected style
    float candleOpen, candleHigh, candleLow, candleClose;

    if (Input_CandleStyle.GetIndex() == 1) // Heiken Ashi
    {
        candleOpen = haOpen;
        candleHigh = haHigh;
        candleLow = haLow;
        candleClose = haClose;
    }
    else // Regular OHLC
    {
        candleOpen = sc.Open[sc.Index];
        candleHigh = sc.High[sc.Index];
        candleLow = sc.Low[sc.Index];
        candleClose = sc.Close[sc.Index];
    }

    // Store candle values
    Subgraph_CandleOpen[sc.Index] = candleOpen;
    Subgraph_CandleHigh[sc.Index] = candleHigh;
    Subgraph_CandleLow[sc.Index] = candleLow;
    Subgraph_CandleClose[sc.Index] = candleClose;

    // Map delta to color
    COLORREF finalColor = Input_NeutralColor.GetColor();
    
    if (avgAbsDelta > 0 && Input_GradientScale.GetFloat() > 0)
    {
        double normalizedDelta = smoothedDelta / (avgAbsDelta * Input_GradientScale.GetFloat());
        
        // Clamp to [-1, 1] range
        if (normalizedDelta > 1.0) normalizedDelta = 1.0;
        if (normalizedDelta < -1.0) normalizedDelta = -1.0;

        if (normalizedDelta >= 0)
        {
            // Interpolate between neutral and strong buy color
            finalColor = LerpColor(Input_NeutralColor.GetColor(), Input_StrongBuyColor.GetColor(), (float)normalizedDelta);
        }
        else
        {
            // Interpolate between neutral and strong sell color
            finalColor = LerpColor(Input_NeutralColor.GetColor(), Input_StrongSellColor.GetColor(), (float)(-normalizedDelta));
        }
    }

    // Draw the colored candle
    s_UseTool CandleTool;
    CandleTool.Clear();
    CandleTool.ChartNumber = sc.ChartNumber;
    CandleTool.AddMethod = UTAM_ADD_OR_ADJUST;
    CandleTool.LineNumber = sc.Index + 2000000; // Unique ID for each candle

    // Draw candle body
    CandleTool.DrawingType = DRAWING_RECTANGLEHIGHLIGHT;
    CandleTool.BeginIndex = sc.Index;
    CandleTool.EndIndex = sc.Index;
    CandleTool.BeginValue = candleOpen;
    CandleTool.EndValue = candleClose;
    CandleTool.Color = finalColor;
    CandleTool.SecondaryColor = finalColor;
    CandleTool.TransparencyLevel = 30;
    CandleTool.LineWidth = 1;

    sc.UseTool(CandleTool);

    // Draw upper wick if exists
    if (candleHigh > max(candleOpen, candleClose))
    {
        s_UseTool WickTool;
        WickTool.Clear();
        WickTool.ChartNumber = sc.ChartNumber;
        WickTool.DrawingType = DRAWING_LINE;
        WickTool.AddMethod = UTAM_ADD_OR_ADJUST;
        WickTool.LineNumber = sc.Index + 3000000; // Unique ID for upper wick
        WickTool.BeginIndex = sc.Index;
        WickTool.EndIndex = sc.Index;
        WickTool.BeginValue = max(candleOpen, candleClose);
        WickTool.EndValue = candleHigh;
        WickTool.Color = finalColor;
        WickTool.LineWidth = 1;

        sc.UseTool(WickTool);
    }

    // Draw lower wick if exists
    if (candleLow < min(candleOpen, candleClose))
    {
        s_UseTool WickTool;
        WickTool.Clear();
        WickTool.ChartNumber = sc.ChartNumber;
        WickTool.DrawingType = DRAWING_LINE;
        WickTool.AddMethod = UTAM_ADD_OR_ADJUST;
        WickTool.LineNumber = sc.Index + 4000000; // Unique ID for lower wick
        WickTool.BeginIndex = sc.Index;
        WickTool.EndIndex = sc.Index;
        WickTool.BeginValue = min(candleOpen, candleClose);
        WickTool.EndValue = candleLow;
        WickTool.Color = finalColor;
        WickTool.LineWidth = 1;

        sc.UseTool(WickTool);
    }
}

/*==========================================================================*/
// Helper function to interpolate between two colors
COLORREF LerpColor(COLORREF color1, COLORREF color2, float t)
{
    if (t < 0.0f) t = 0.0f;
    if (t > 1.0f) t = 1.0f;

    int r1 = GetRValue(color1);
    int g1 = GetGValue(color1);
    int b1 = GetBValue(color1);

    int r2 = GetRValue(color2);
    int g2 = GetGValue(color2);
    int b2 = GetBValue(color2);

    int r = (int)(r1 + (r2 - r1) * t);
    int g = (int)(g1 + (g2 - g1) * t);
    int b = (int)(b1 + (b2 - b1) * t);

    // Clamp values to [0, 255]
    r = max(0, min(255, r));
    g = max(0, min(255, g));
    b = max(0, min(255, b));

    return RGB(r, g, b);
}