#include "sierrachart.h"

SCDLLName("Delta Heatmap Smooth Study")

// Function declaration
COLORREF LerpColor(COLORREF color1, COLORREF color2, float t);

/*==========================================================================*/
SCSFExport scsf_DeltaHeatmapSmooth(SCStudyInterfaceRef sc)
{
    // Input references
    SCInputRef Input_Smoothing = sc.Input[0];
    SCInputRef Input_GradientScale = sc.Input[1];
    SCInputRef Input_StrongBuyColor = sc.Input[2];
    SCInputRef Input_StrongSellColor = sc.Input[3];
    SCInputRef Input_NeutralColor = sc.Input[4];

    // Subgraph references for internal calculations
    SCSubgraphRef Subgraph_SmoothedDelta = sc.Subgraph[0];
    SCSubgraphRef Subgraph_AvgAbsDelta = sc.Subgraph[1];

    // Color bar subgraph for displaying colors
    SCSubgraphRef Subgraph_ColorBar = sc.Subgraph[2];

    if (sc.SetDefaults)
    {
        // Study configuration
        sc.GraphName = "Delta Heatmap Smooth";
        sc.StudyDescription = "Heatmap overlay that colors candle bodies, borders and shadows based on buy/sell pressure using smoothed delta values";
        sc.GraphRegion = 0; // Main price region
        sc.AutoLoop = 1;    // Enable auto-looping
        sc.UpdateAlways = 1; // Update on every tick

        // Input parameters
        Input_Smoothing.Name = "Lookback Smoothing (bars)";
        Input_Smoothing.SetInt(20);
        Input_Smoothing.SetIntLimits(1, 500);

        Input_GradientScale.Name = "Gradient Scale (x Avg|Delta|)";
        Input_GradientScale.SetFloat(2.0f);
        Input_GradientScale.SetFloatLimits(0.1f, 10.0f);

        Input_StrongBuyColor.Name = "Strong Buy Color";
        Input_StrongBuyColor.SetColor(RGB(0, 179, 255));

        Input_StrongSellColor.Name = "Strong Sell Color";
        Input_StrongSellColor.SetColor(RGB(255, 0, 213));

        Input_NeutralColor.Name = "Neutral Color";
        Input_NeutralColor.SetColor(RGB(128, 128, 128));

        // Subgraphs for calculations (not displayed)
        Subgraph_SmoothedDelta.Name = "Smoothed Delta";
        Subgraph_SmoothedDelta.DrawStyle = DRAWSTYLE_IGNORE;

        Subgraph_AvgAbsDelta.Name = "Average Absolute Delta";
        Subgraph_AvgAbsDelta.DrawStyle = DRAWSTYLE_IGNORE;

        // Color bar subgraph for candle coloring
        Subgraph_ColorBar.Name = "Color Bar";
        Subgraph_ColorBar.DrawStyle = DRAWSTYLE_COLOR_BAR;
        Subgraph_ColorBar.PrimaryColor = RGB(128, 128, 128);
        Subgraph_ColorBar.SecondaryColor = RGB(128, 128, 128);
        Subgraph_ColorBar.SecondaryColorUsed = 1;

        return;
    }

    // Get bid/ask volume data
    float bidVolume = sc.BidVolume[sc.Index];
    float askVolume = sc.AskVolume[sc.Index];
    
    // If no bid/ask data available, try to use NumberOfTrades as fallback
    if (bidVolume == 0 && askVolume == 0)
    {
        return; // No volume data available
    }
    
    double currentDelta = bidVolume - askVolume;

    // Calculate weighted smoothed delta
    double smoothedDelta = 0.0;
    double weightSum = 0.0;
    int smoothingPeriod = Input_Smoothing.GetInt();

    for (int i = 0; i < smoothingPeriod && sc.Index - i >= 0; i++)
    {
        // Get delta for bar at index (sc.Index - i)
        int barIndex = sc.Index - i;
        double barDelta = sc.BidVolume[barIndex] - sc.AskVolume[barIndex];

        // Weight calculation (more recent bars have higher weight)
        double weight = smoothingPeriod - i;
        smoothedDelta += barDelta * weight;
        weightSum += weight;
    }

    if (weightSum > 0)
        smoothedDelta /= weightSum;

    Subgraph_SmoothedDelta[sc.Index] = (float)smoothedDelta;

    // Calculate average absolute delta
    double avgAbsDelta = 0.0;
    int validBars = 0;

    for (int i = 0; i < smoothingPeriod && sc.Index - i >= 0; i++)
    {
        int barIndex = sc.Index - i;
        double barDelta = sc.BidVolume[barIndex] - sc.AskVolume[barIndex];
        
        avgAbsDelta += abs(barDelta);
        validBars++;
    }

    if (validBars > 0)
        avgAbsDelta /= validBars;
    else
        avgAbsDelta = 1.0; // Default value to avoid division by zero

    Subgraph_AvgAbsDelta[sc.Index] = (float)avgAbsDelta;

    // Map delta to color
    COLORREF finalColor = Input_NeutralColor.GetColor();
    
    if (avgAbsDelta > 0 && Input_GradientScale.GetFloat() > 0)
    {
        double normalizedDelta = smoothedDelta / (avgAbsDelta * Input_GradientScale.GetFloat());
        
        // Clamp to [-1, 1] range
        if (normalizedDelta > 1.0) normalizedDelta = 1.0;
        if (normalizedDelta < -1.0) normalizedDelta = -1.0;

        if (normalizedDelta >= 0)
        {
            // Interpolate between neutral and strong buy color
            finalColor = LerpColor(Input_NeutralColor.GetColor(), Input_StrongBuyColor.GetColor(), (float)normalizedDelta);
        }
        else
        {
            // Interpolate between neutral and strong sell color
            finalColor = LerpColor(Input_NeutralColor.GetColor(), Input_StrongSellColor.GetColor(), (float)(-normalizedDelta));
        }
    }

    // Apply color to candle body, border, and wicks
    sc.Subgraph[SC_OPEN].DataColor[sc.Index] = finalColor;
    sc.Subgraph[SC_HIGH].DataColor[sc.Index] = finalColor;
    sc.Subgraph[SC_LOW].DataColor[sc.Index] = finalColor;
    sc.Subgraph[SC_LAST].DataColor[sc.Index] = finalColor;
}

/*==========================================================================*/
// Helper function to interpolate between two colors
COLORREF LerpColor(COLORREF color1, COLORREF color2, float t)
{
    if (t < 0.0f) t = 0.0f;
    if (t > 1.0f) t = 1.0f;

    int r1 = GetRValue(color1);
    int g1 = GetGValue(color1);
    int b1 = GetBValue(color1);

    int r2 = GetRValue(color2);
    int g2 = GetGValue(color2);
    int b2 = GetBValue(color2);

    int r = (int)(r1 + (r2 - r1) * t);
    int g = (int)(g1 + (g2 - g1) * t);
    int b = (int)(b1 + (b2 - b1) * t);

    // Clamp values to [0, 255]
    r = max(0, min(255, r));
    g = max(0, min(255, g));
    b = max(0, min(255, b));

    return RGB(r, g, b);
}