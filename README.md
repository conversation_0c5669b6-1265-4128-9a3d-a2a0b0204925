# SierraChart Drawing Exporter

This SierraChart study exports chart drawings (zones, lines, text, etc.) to a CSV file with detailed information including prices and colors.

## Features

- Exports user-drawn chart drawings to CSV format
- Includes drawing prices (BeginValue, EndValue) for zone analysis
- Extracts and exports drawing colors in multiple formats (RGB hex, individual R/G/B values)
- Supports multiple drawing types: rectangles, lines, text, and more
- Configurable export options
- Manual export button for on-demand exports

## Installation

1. Copy `DrawingExporter.cpp` to your SierraChart `ACS_Source` folder
2. Compile the study using SierraChart's build system
3. Add the study to your chart

## Usage

### Study Inputs

1. **Export on Update** - Automatically export when the study updates (default: No)
2. **Output File Path** - Path where the CSV file will be saved (default: `C:\Temp\drawings_export.csv`)
3. **Include Rectangle Highlights** - Export rectangle/zone drawings (default: Yes)
4. **Include Lines** - Export line drawings (default: Yes)
5. **Include Text** - Export text drawings (default: Yes)
6. **Export Now** - Button to manually trigger export

### CSV Output Format

The exported CSV file contains the following columns:

- **DrawingType** - Type of drawing (e.g., "Rectangle Highlight", "Line", "Text")
- **LineNumber** - Internal line number identifier
- **BeginDateTime** - Start date/time of the drawing
- **EndDateTime** - End date/time of the drawing
- **BeginValue** - Start price value (useful for zone analysis)
- **EndValue** - End price value (useful for zone analysis)
- **Color_RGB** - Color in hex format (e.g., "#FF0000" for red)
- **Color_Red** - Red component (0-255)
- **Color_Green** - Green component (0-255)
- **Color_Blue** - Blue component (0-255)
- **Text** - Text content (for text drawings)

### Supported Drawing Types

- Rectangle Highlight (zones)
- Extended Rectangle Highlight
- Lines
- Rays
- Horizontal Lines
- Vertical Lines
- Extended Lines
- Text
- Stationary Text
- Arrows
- Ellipse Highlights
- Retracements
- Pitchforks
- Parallel Lines/Rays
- Triangles
- Angled Ellipses

## Example CSV Output

```csv
DrawingType,LineNumber,BeginDateTime,EndDateTime,BeginValue,EndValue,Color_RGB,Color_Red,Color_Green,Color_Blue,Text
Rectangle Highlight,1,2024-01-15 09:30:00,2024-01-15 16:00:00,4150.250000,4175.750000,#FF0000,255,0,0,
Horizontal Line,2,2024-01-15 10:00:00,2024-01-15 10:00:00,4160.000000,4160.000000,#00FF00,0,255,0,
Text,3,2024-01-15 11:30:00,2024-01-15 11:30:00,4165.500000,4165.500000,#0000FF,0,0,255,Support Zone
```

## Notes

- The study only exports user-drawn drawings, not study-generated drawings
- Make sure the output directory exists before running the export
- The study will log success/failure messages to the SierraChart message log
- For zone analysis, use BeginValue and EndValue to determine price ranges
- Colors are extracted from the drawing's primary color property

## Troubleshooting

- If export fails, check that the output directory exists and is writable
- Ensure you have drawings on the chart before attempting export
- Check the SierraChart message log for detailed error information
